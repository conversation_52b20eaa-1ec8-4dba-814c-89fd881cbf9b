# Pac-Map 專案

## 測試方式
使用 VS Code 的 "Live Server" 擴充功能 (推薦)

這是最簡單、最方便的方法，幾乎是所有網頁開發者的標配。

1.  在 VS Code 中打開您的 `Pac-Map` 專案資料夾。
2.  點擊左側的**擴充功能**圖示（四個方塊疊在一起的圖案）。
3.  在搜尋框中輸入 `Live Server`。
4.  找到由 Ritwick Dey 開發的那個，點擊 **Install (安裝)**。
5.  安裝完成後，回到您的 `index.html` 檔案。
6.  在檔案上按**右鍵**，然後選擇 `Open with Live Server`。
7.  這會自動在您的預設瀏覽器中打開一個新分頁，網址會像這樣：`http://127.0.0.1:5500/index.html`。


## 專案架構

### 1. `index.html` - 房子的藍圖與結構

*   **它的工作：** 這個檔案是整個遊戲的**骨架和外殼**。它定義了遊戲畫面上所有可見的元素，像是遊戲標題、開始按鈕、地圖畫布、分數板、暫停畫面等等。
*   **它如何工作：** 它使用 HTML 標籤（如 `<div>`, `<h1>`, `<button>`）來搭建這些結構。同時，它也像一個總指揮，透過 `<link>` 標籤引入 CSS 樣式表來美化外觀，並透過 `<script>` 標籤引入 JavaScript 檔案，賦予這棟房子生命和互動能力。
*   **比喻：** 它是建築藍圖，決定了哪裡是客廳（主畫面）、哪裡是臥室（遊戲畫面）、窗戶（按鈕）在哪裡。

---

### 2. `js/main.js` - 大門口的總機與接待員

*   **它的工作：** 這是所有 JavaScript 程式碼的**入口點 (Entry Point)**。當玩家打開遊戲時，這個檔案是第一個被執行的腳本。
*   **它如何工作：** 它的主要任務是**「設定」和「串連」**。它會：
    1.  確保所有東西都準備好了（`DOMContentLoaded`）。
    2.  初始化音效、開發者工具等模組。
    3.  為 `index.html` 裡的所有按鈕（開始、說明、暫停等）綁定對應的功能（`addEventListener`）。例如，告訴「開始遊戲」按鈕：「嘿，如果有人按你，你就去呼叫 `game.js` 裡的 `initGame` 函數。」
    4.  監聽玩家的鍵盤輸入，並將指令傳遞給遊戲核心。
*   **比喻：** 他是房子的總機，負責接聽所有來自外部的電話（玩家操作），然後將需求轉接到正確的部門去處理。

---

### 3. `js/gameState.js` - 中央控制室的儀表板

*   **它的工作：** 這個檔案是遊戲的**「大腦記憶中樞」和「設定手冊」**。它不執行複雜的動作，只負責**儲存和管理**所有重要的遊戲狀態和數據。
*   **它如何工作：** 它定義了大量的變數，例如：
    *   `gameState`: 包含了玩家目前的分數、生命值、等級、小精靈和鬼的位置等所有會動態改變的資訊。
    *   `mapConfigs`: 儲存了台北、台中、高雄等地圖的固定設定（中心點、縮放等級）。
    *   遊戲常數：如小精靈的速度、鬼的數量等不會輕易改變的數值。
*   **比喻：** 它是中央控制室牆上的一整排儀表板和顯示螢幕。所有其他部門（檔案）都需要看著這些儀表板上的數據來決定自己該做什麼，同時也會回來更新這些數據。

---

### 4. `js/game.js` - 遊戲的核心引擎室

*   **它的工作：** 這是**遊戲最核心的邏輯**所在。所有遊戲的進行、規則判斷、流程控制都在這裡。
*   **它如何工作：** 它包含了一系列關鍵功能：
    *   `initGame`: 初始化一場新遊戲，呼叫其他模組來生成地圖、小精靈、鬼怪和豆子。
    *   `gameLoop`: 遊戲的主迴圈，像心臟一樣規律跳動，在每一幀更新所有遊戲物件的位置並檢查碰撞。
    *   `loseLife`, `nextLevel`, `endGame`: 控制遊戲的關鍵流程，如失去生命、進入下一關、遊戲結束。
    *   `pauseGame`, `resumeGame`: 處理遊戲的暫停與繼續。
*   **比喻：** 它是房子的引擎室或發電機。只要它在運轉，整個房子（遊戲）就在活動。它驅動著所有事情的發生。

---

### 5. `js/ui.js` - 內部裝潢與資訊公告團隊

*   **它的工作：** 專門負責**更新使用者介面 (UI)**，也就是將 `gameState.js` 中的數據顯示給玩家看。
*   **它如何工作：** 它裡面的函數會去抓取 `index.html` 中的元素（例如分數的 `<span>`），然後把 `gameState` 中最新的分數、生命值等數據填進去。它也負責切換小精靈圖示的方向。
*   **比喻：** 他是負責在房子各處的公告螢幕上更新最新資訊的團隊，確保住戶（玩家）能看到即時的狀態。

---

### 6. `js/map.js` - 地圖繪製與道路施工隊

*   **它的工作：** 專門處理所有和**地圖、路網**相關的繁重工作。
*   **它如何工作：**
    *   `fetchRoadData`: 向外部的 OpenStreetMap API 發送請求，取得真實世界的街道資料。
    *   `generateRoadNetworkGeneric`: 處理這些原始資料，將其轉換成遊戲可以使用的節點和路徑網路（也就是小精靈和鬼可以走的路）。
    *   `drawVisualRoads`: 在畫面上把這些路網視覺化地畫出來，變成藍色邊框的黑色道路。
*   **比喻：** 他是遊戲世界的建築師和施工隊，負責把一片荒地（真實地圖）變成一個規劃好的迷宮賽道。

---

### 7. `js/ai.js` - 鬼怪與自動駕駛的智慧核心

*   **它的工作：** 負責所有需要**「思考」**的邏輯。
*   **它如何工作：**
    *   `aStarSearch`: 實作了 A* 尋路演算法，這是所有 AI 的基礎。它能計算出從 A 點到 B 點的最短路徑。
    *   `decideNextGhostMoves`: 鬼怪的大腦。它會利用 A* 演算法來決定下一步應該追擊玩家，還是逃跑。
    *   `manageAutoPilot`: 開發者模式中「自動駕駛」功能的核心，控制小精靈自動去吃豆子。
*   **比喻：** 他是房子裡的智慧中樞，賦予了 NPC（鬼怪）行為模式和思考能力。

---

### 8. `js/audio.js` - 音響與音效控制中心

*   **它的工作：** 管理遊戲中所有的**聲音效果**。
*   **它如何工作：** 它使用 `Tone.js` 這個函式庫來定義各種音效（吃豆、吃鬼、死亡、開始遊戲），並提供簡單的函數（如 `playDotSound()`）讓其他檔案在需要時呼叫播放。
*   **比喻：** 他是房子的音響系統和 DJ，在適當的時機播放背景音樂和音效，增加氣氛。

---

### 9. `js/backgroundAnimation.js` - 大廳的動態裝飾

*   **它的工作：** 專門負責在**開始主畫面**顯示的那個動態背景地圖。
*   **它如何工作：** 它其實是一個獨立運作的「迷你版」遊戲。它會自己加載地圖、生成路網，然後創建一個小精靈和幾隻鬼在上面隨機移動，給玩家一種遊戲正在動態展示的感覺。當玩家正式開始遊戲後，這個背景動畫就會停止並被清理掉。
*   **比喻：** 他是房子大廳裡那個漂亮的動態水幕牆或大型電子看板，用來吸引訪客的目光。

---

### 10. `js/devConsole.js` - 隱藏的工程師後門

*   **它的工作：** 提供一個**開發者作弊工具**。
*   **它如何工作：** 它監聽特定的按鍵（` `` ` 鍵）來打開一個指令輸入框。玩家（或開發者）可以在裡面輸入作弊碼，如 `godmode`（無敵）、`nl`（直接過關）等，來方便測試或遊玩。
*   **比喻：** 他是建築師留下的秘密通道和控制面板，只有知道密碼的人才能進入，並對房子的規則進行修改。
