import { gameState } from './gameState.js';

// Canvas overlay for rendering dots and power pellets
export class CanvasRenderer {
    constructor() {
        this.canvas = null;
        this.ctx = null;
        this.animationTime = 0;
        this.lastRender = 0;
    }

    // 初始化 Canvas overlay
    initCanvas(map) {
        if (this.canvas) {
            this.removeCanvas();
        }

        // 創建 Canvas 元素
        this.canvas = document.createElement('canvas');
        this.canvas.style.position = 'absolute';
        this.canvas.style.top = '0';
        this.canvas.style.left = '0';
        this.canvas.style.pointerEvents = 'none'; // 不阻擋地圖互動
        this.canvas.style.zIndex = '400'; // 在地圖上方，但在 UI 下方
        
        this.ctx = this.canvas.getContext('2d');
        
        // 將 Canvas 添加到地圖容器
        const mapContainer = map.getContainer();
        mapContainer.appendChild(this.canvas);
        
        // 設置 Canvas 尺寸
        this.resizeCanvas();
        
        // 監聽地圖事件
        map.on('viewreset', () => this.resizeCanvas());
        map.on('zoom', () => this.resizeCanvas());
        map.on('move', () => this.render());
        
        gameState.canvasOverlay = this;
    }

    // 調整 Canvas 尺寸
    resizeCanvas() {
        if (!this.canvas || !gameState.map) return;
        
        const mapContainer = gameState.map.getContainer();
        const rect = mapContainer.getBoundingClientRect();
        
        this.canvas.width = rect.width;
        this.canvas.height = rect.height;
        this.canvas.style.width = rect.width + 'px';
        this.canvas.style.height = rect.height + 'px';
        
        this.render();
    }

    // 移除 Canvas
    removeCanvas() {
        if (this.canvas && this.canvas.parentNode) {
            this.canvas.parentNode.removeChild(this.canvas);
        }
        this.canvas = null;
        this.ctx = null;
        gameState.canvasOverlay = null;
    }

    // 將經緯度轉換為 Canvas 像素座標
    latLngToCanvasPoint(lat, lng) {
        if (!gameState.map) return null;
        
        const point = gameState.map.latLngToContainerPoint([lat, lng]);
        return { x: point.x, y: point.y };
    }

    // 主要渲染函數
    render() {
        if (!this.ctx || !gameState.map) return;

        // 清除 Canvas
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

        // 更新動畫時間
        this.animationTime = performance.now();

        // 渲染 dots
        this.renderDots();

        // 渲染 power pellets
        this.renderPowerPellets();

        // 渲染 POI markers
        this.renderPOIs();
    }

    // 渲染普通 dots
    renderDots() {
        this.ctx.fillStyle = '#ffff00'; // 黃色
        
        gameState.dots.forEach(dot => {
            if (!dot.position) return;
            
            const canvasPoint = this.latLngToCanvasPoint(dot.position[0], dot.position[1]);
            if (!canvasPoint) return;
            
            this.ctx.beginPath();
            this.ctx.arc(canvasPoint.x, canvasPoint.y, 2, 0, 2 * Math.PI);
            this.ctx.fill();
        });
    }

    // 渲染 power pellets（帶閃爍動畫）
    renderPowerPellets() {
        // 計算閃爍透明度（1秒週期）
        const blinkCycle = (this.animationTime % 1000) / 1000;
        const opacity = blinkCycle < 0.5 ? 1 : 0.3;
        
        this.ctx.fillStyle = `rgba(255, 255, 0, ${opacity})`;
        
        gameState.powerPellets.forEach(pellet => {
            if (!pellet.position) return;
            
            const canvasPoint = this.latLngToCanvasPoint(pellet.position[0], pellet.position[1]);
            if (!canvasPoint) return;
            
            this.ctx.beginPath();
            this.ctx.arc(canvasPoint.x, canvasPoint.y, 6, 0, 2 * Math.PI);
            this.ctx.fill();
        });
    }

    // 更新動畫（在 gameLoop 中調用）- 節流渲染
    updateAnimation() {
        if (!this.ctx) return;

        // 節流 Canvas 渲染：每 33ms (30 FPS) 渲染一次
        const now = performance.now();
        if (!this.lastRender || now - this.lastRender >= 33) {
            this.render();
            this.lastRender = now;
        }
    }
}

// 創建全域實例
export const canvasRenderer = new CanvasRenderer();
